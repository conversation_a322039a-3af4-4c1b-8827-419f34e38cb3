from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views import ChatSessionViewSet, ChatMessageViewSet

router = DefaultRouter()
router.register(r'sessions', ChatSessionViewSet, basename='chat-session')
router.register(r'sessions/(?P<session_id>\d+)/messages', ChatMessageViewSet, basename='chat-message')

urlpatterns = [
    path('', include(router.urls)),
]