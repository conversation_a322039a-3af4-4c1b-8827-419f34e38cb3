from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import ChatSession, ChatMessage
from .serializers import (
    ChatSessionSerializer,
    ChatMessageSerializer,
    ChatRequestSerializer,
    ChatResponseSerializer
)
from users.utils import increment_chat_count, get_usage_stats, UsageLimitExceeded
import requests
import os

class ChatSessionViewSet(viewsets.ModelViewSet):
    serializer_class = ChatSessionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ChatSession.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['post'])
    def send_message(self, request, pk=None):
        session = self.get_object()
        message = request.data.get('message')
        model = request.data.get('model', 'openai')
        context = request.data.get('context')

        if not message:
            return Response(
                {"error": "Message is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Call FastAPI inference endpoint
            inference_url = f"http://{os.getenv('FASTAPI_HOST', 'localhost:8001')}/infer"
            response = requests.post(
                inference_url,
                json={
                    "model": model,
                    "message": message,
                    "context": context
                },
                headers={"Authorization": f"Bearer {request.auth}"}
            )
            response.raise_for_status()
            inference_data = response.json()

            # Create user message
            user_message = ChatMessage.objects.create(
                session=session,
                role='user',
                content=message,
                model=model
            )

            # Create assistant message
            assistant_message = ChatMessage.objects.create(
                session=session,
                role='assistant',
                content=inference_data['response'],
                model=model,
                tokens=inference_data['tokens']
            )

            # Update session title if it's the first message
            if not session.title:
                session.title = message[:50] + "..." if len(message) > 50 else message
                session.save()

            return Response({
                "session_id": session.id,
                "message": inference_data['response'],
                "model": model,
                "tokens": inference_data['tokens'],
                "usage_stats": inference_data['usage_stats']
            })

        except requests.exceptions.RequestException as e:
            return Response(
                {"error": f"Error calling inference service: {str(e)}"},
                status=status.HTTP_503_SERVICE_UNAVAILABLE
            )
        except UsageLimitExceeded as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class ChatMessageViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = ChatMessageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        session_id = self.kwargs.get('session_id')
        session = get_object_or_404(ChatSession, id=session_id, user=self.request.user)
        return ChatMessage.objects.filter(session=session).order_by('created_at')

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['session_id'] = self.kwargs.get('session_id')
        return context