from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
import logging

from .models import Document, DocumentEmbedding, BlueprintTopics
from .serializers import DocumentSerializer, DocumentEmbeddingSerializer, BlueprintTopicsSerializer

logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def store_embeddings(request, document_id):
    """
    Store document embeddings received from FastAPI server
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the chunks from the request
        chunks = request.data.get('chunks', [])

        if not chunks:
            return Response(
                {"error": "No chunks provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # First delete any existing embeddings for this document
        DocumentEmbedding.objects.filter(document=document).delete()

        # Create embedding objects for each chunk
        for chunk in chunks:
            DocumentEmbedding.objects.create(
                document=document,
                text_chunk=chunk['text'],
                embedding=chunk['embedding'],
                chunk_number=chunk['chunk_number']
            )

        # Update document status to completed
        document.processing_status = 'completed'
        document.error_message = None  # Clear any previous error
        document.save()

        return Response({
            "message": "Embeddings stored successfully",
            "document_id": document_id,
            "num_chunks": len(chunks)
        })

    except Exception as e:
        logger.error(f"Error storing embeddings: {str(e)}")
        return Response(
            {"error": f"Error storing embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def store_topics(request, document_id):
    """
    Store blueprint topics received from FastAPI server
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the topics from the request
        topics = request.data.get('topics', [])

        if not topics:
            return Response(
                {"error": "No topics provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # First delete any existing topics for this document
        BlueprintTopics.objects.filter(document=document).delete()

        # Create topic objects for each topic
        created_topics = []
        for topic in topics:
            blueprint_topic = BlueprintTopics.objects.create(
                document=document,
                title=topic['title'],
                weightage=topic['weightage']
            )

            # Find relevant embeddings for this topic
            embeddings = DocumentEmbedding.objects.filter(document=document)

            if embeddings.exists():
                # Use semantic search to find relevant embeddings
                from sentence_transformers import SentenceTransformer
                import numpy as np

                # Initialize the sentence transformer model
                model = SentenceTransformer('all-MiniLM-L6-v2')

                # Generate embedding for the topic
                topic_embedding = model.encode([topic['title']])[0]

                # Calculate similarity scores
                similarities = []
                for idx, embedding_obj in enumerate(embeddings):
                    embedding_vector = np.array(embedding_obj.embedding)
                    similarity = np.dot(topic_embedding, embedding_vector) / (
                        np.linalg.norm(topic_embedding) * np.linalg.norm(embedding_vector)
                    )
                    similarities.append((similarity, idx, embedding_obj))

                # Sort by similarity (highest first)
                similarities.sort(reverse=True)

                # Get top 5 or 30% of embeddings, whichever is greater
                num_to_select = max(5, int(len(embeddings) * 0.3))
                relevant_embeddings = [emb for _, _, emb in similarities[:num_to_select]]

                # Add embeddings to the topic
                blueprint_topic.content.add(*relevant_embeddings)

            created_topics.append(blueprint_topic)

        # Update document status to completed
        document.processing_status = 'completed'
        document.error_message = None  # Clear any previous error
        document.save()

        return Response({
            "message": "Topics stored successfully",
            "document_id": document_id,
            "num_topics": len(created_topics)
        })

    except Exception as e:
        logger.error(f"Error storing topics: {str(e)}")
        return Response(
            {"error": f"Error storing topics: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_document_status(request, document_id):
    """
    Update document status
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the status from the request
        status_value = request.data.get('status')
        error_message = request.data.get('error_message')

        if not status_value:
            return Response(
                {"error": "No status provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update document status
        document.processing_status = status_value
        if error_message:
            document.error_message = error_message
        document.save()

        return Response({
            "message": "Document status updated successfully",
            "document_id": document_id,
            "status": status_value
        })

    except Exception as e:
        logger.error(f"Error updating document status: {str(e)}")
        return Response(
            {"error": f"Error updating document status: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def find_relevant_embeddings(request, document_id):
    """
    Find embeddings relevant to a topic
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the topic from the request
        topic = request.data.get('topic')

        if not topic:
            return Response(
                {"error": "No topic provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get all embeddings for this document
        embeddings = DocumentEmbedding.objects.filter(document=document)

        if not embeddings.exists():
            return Response(
                {"error": "Document has no embeddings"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Use semantic search to find relevant embeddings
        from sentence_transformers import SentenceTransformer
        import numpy as np

        # Initialize the sentence transformer model
        model = SentenceTransformer('all-MiniLM-L6-v2')

        # Generate embedding for the topic
        topic_embedding = model.encode([topic])[0]

        # Calculate similarity scores
        similarities = []
        for idx, embedding_obj in enumerate(embeddings):
            embedding_vector = np.array(embedding_obj.embedding)
            similarity = np.dot(topic_embedding, embedding_vector) / (
                np.linalg.norm(topic_embedding) * np.linalg.norm(embedding_vector)
            )
            similarities.append((similarity, idx, embedding_obj))

        # Sort by similarity (highest first)
        similarities.sort(reverse=True)

        # Get top 5 or 30% of embeddings, whichever is greater
        num_to_select = max(5, int(len(embeddings) * 0.3))
        relevant_embeddings = [emb for _, _, emb in similarities[:num_to_select]]

        return Response({
            "embedding_ids": [emb.id for emb in relevant_embeddings]
        })

    except Exception as e:
        logger.error(f"Error finding relevant embeddings: {str(e)}")
        return Response(
            {"error": f"Error finding relevant embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_document_embeddings(request, document_id):
    """
    Get all embeddings for a document
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get all embeddings for this document
        embeddings = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')

        if not embeddings.exists():
            return Response(
                {"error": "Document has no embeddings"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Serialize the embeddings
        serializer = DocumentEmbeddingSerializer(embeddings, many=True)

        return Response(serializer.data)

    except Exception as e:
        logger.error(f"Error retrieving document embeddings: {str(e)}")
        return Response(
            {"error": f"Error retrieving document embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
